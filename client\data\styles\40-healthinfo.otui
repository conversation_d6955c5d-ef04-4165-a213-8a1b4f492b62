ExperienceBar < ProgressBar
  id: experienceBar
  background-color: #B6E866
  anchors.top: prev.bottom
  anchors.left: parent.left
  anchors.right: parent.right
  margin: 1
  margin-top: 3

CapLabel < GameLabel
  id: capLabel
  color: white
  font: verdana-11px-rounded
  anchors.bottom: parent.bottom
  anchors.left: parent.left
  anchors.right: parent.horizontalCenter
  margin-top: 5
  margin-left: 3
  on: true

  $!on:
    visible: false
    margin-top: 0
    height: 0

ConditionWidget < UIWidget
  size: 18 18

  $!first:
    margin-left: 2
      
HealthOverlay < UIWidget
  id: healthOverlay
  anchors.fill: parent
  phantom: true
    
  UIProgressBar
    id: healthCircle
    anchors.horizontalCenter: parent.horizontalCenter
    anchors.verticalCenter: parent.verticalCenter
    image-source: /images/game/circle/left_empty
    margin-right: 169
    margin-bottom: 16
    opacity: 0.5
    phantom: true

  UIProgressBar
    id: healthCircleFront
    anchors.horizontalCenter: parent.horizontalCenter
    anchors.verticalCenter: parent.verticalCenter
    image-source: /images/game/circle/left_full
    margin-right: 169
    margin-bottom: 16
    opacity: 0.5
    phantom: true

  UIProgressBar
    id: manaCircle
    anchors.horizontalCenter: parent.horizontalCenter
    anchors.verticalCenter: parent.verticalCenter
    image-source: /images/game/circle/right_empty    
    image-auto-resize: true
    margin-left: 130
    margin-bottom: 16
    opacity: 0.5
    phantom: true

  UIProgressBar
    id: manaCircleFront
    anchors.horizontalCenter: parent.horizontalCenter
    anchors.verticalCenter: parent.verticalCenter
    image-source: /images/game/circle/right_full
    margin-left: 130
    margin-bottom: 16    
    opacity: 0.4
    image-color: #0000FFFF
    phantom: true
  
HealthInfoWindow < MiniWindow
  icon: /images/topbuttons/healthinfo
  !text: tr('Health Info')
  height: 123

  MiniWindowContents
    HealthBar
      id: healthBar
      anchors.top: parent.top
      anchors.left: parent.left
      anchors.right: parent.right
      margin: 2
      margin-top: 1
      
    ManaBar
      id: manaBar
      anchors.top: prev.bottom
      anchors.left: parent.left
      anchors.right: parent.right
      margin: 2
      
    ExperienceBar
    CapLabel

