-- game_playerpanel.lua
-- Manages the player panel UI with dynamic button loading

local playerPanel = nil
local contentPanel = nil
local topButtonsPanel = nil
local bottomButtonsPanel = nil
local currentContent = "none"
local skillCards = {}
local buttons = {}
local equipmentSlots = {}

-- Equipment slot configuration
local equipmentSlotConfigs = {
  head = {
    slotId = 1,
    name = "Head",
    position = {x = 65535, y = 1, z = 0}
  },
  neck = {
    slotId = 2,
    name = "Neck",
    position = {x = 65535, y = 2, z = 0}
  },
  backpack = {
    slotId = 3,
    name = "Backpack",
    position = {x = 65535, y = 3, z = 0}
  },
  armor = {
    slotId = 4,
    name = "Armor",
    position = {x = 65535, y = 4, z = 0}
  },
  right = {
    slotId = 5,
    name = "Right Hand",
    position = {x = 65535, y = 5, z = 0}
  },
  left = {
    slotId = 6,
    name = "Left Hand",
    position = {x = 65535, y = 6, z = 0}
  },
  legs = {
    slotId = 7,
    name = "Legs",
    position = {x = 65535, y = 7, z = 0}
  },
  feet = {
    slotId = 8,
    name = "Feet",
    position = {x = 65535, y = 8, z = 0}
  },
  ring = {
    slotId = 9,
    name = "Ring",
    position = {x = 65535, y = 9, z = 0}
  },
  ammo = {
    slotId = 10,
    name = "Ammo",
    position = {x = 65535, y = 10, z = 0}
  }
}

-- Button configuration - defines which buttons are available and where they go
local buttonConfigs = {
  -- Top buttons
  stats = {
    text = "Stats",
    position = "top",
    order = 1,
    enabled = true,
    onClick = function() showContent('stats') end
  },
  gear = {
    text = "Gear",
    position = "top",
    order = 2,
    enabled = true, -- Not implemented yet
    onClick = function() showContent('gear') end
  },
  inventory = {
    text = "Inventory",
    position = "top",
    order = 3,
    enabled = true, -- Not implemented yet
    onClick = function() showContent('inventory') end
  },
  pvp = {
    text = "PvP",
    position = "top",
    order = 4,
    enabled = true, -- Not implemented yet
    onClick = function() showContent('pvp') end
  },
  battle = {
    text = "Battle",
    position = "top",
    order = 5,
    enabled = true, -- Not implemented yet
    onClick = function() showContent('battle') end
  },

  -- Bottom buttons
  quests = {
    text = "Quests",
    position = "bottom",
    order = 1,
    enabled = true, -- Not implemented yet
    onClick = function() showContent('inventory') end
  },
  spells = {
    text = "Spells",
    position = "bottom",
    order = 2,
    enabled = true, -- Not implemented yet
    onClick = function() showContent('spells') end
  },
  options = {
    text = "Options",
    position = "bottom",
    order = 3,
    enabled = true,
    onClick = function() showContent('options') end
  },
  help = {
    text = "Help",
    position = "bottom",
    order = 4,
    enabled = true, -- Not implemented yet
    onClick = function() showContent('help') end
  },
  logout = {
    text = "Logout",
    position = "bottom",
    order = 5,
    enabled = true, -- Not implemented yet
    onClick = function() showContent('logout') end
  }
}

-- Skill configuration table
local skillConfigs = {
  ninjutsu = {
    name = "Ninjutsu",
    color = "#4a90e2", -- Blue
    getValue = function(player) return tonumber(player:getNinjutsu()) or 0 end,
    getPercent = function(player) return tonumber(player:getNinjutsuPercent()) or 0 end,
    getBase = function(player) return tonumber(player:getBaseNinjutsu()) or 0 end
  },
  taijutsu = {
    name = "Taijutsu",
    color = "#e74c3c", -- Red
    getValue = function(player)
      -- Use generic skill system (Taijutsu = 0 in client enum)
      return tonumber(player:getSkillLevel(0)) or 0
    end,
    getPercent = function(player)
      return tonumber(player:getSkillLevelPercent(0)) or 0
    end,
    getBase = function(player)
      return tonumber(player:getSkillBaseLevel(0)) or 0
    end
  },
  kenjutsu = {
    name = "Kenjutsu",
    color = "#f39c12", -- Orange
    getValue = function(player)
      -- Use generic skill system (Kenjutsu = 1 in client enum)
      return tonumber(player:getSkillLevel(1)) or 0
    end,
    getPercent = function(player)
      return tonumber(player:getSkillLevelPercent(1)) or 0
    end,
    getBase = function(player)
      return tonumber(player:getSkillBaseLevel(1)) or 0
    end
  },
  shurikenjutsu = {
    name = "Shurikenjutsu",
    color = "#9b59b6", -- Purple
    getValue = function(player)
      -- Use generic skill system (Shurikenjutsu = 2 in client enum)
      return tonumber(player:getSkillLevel(2)) or 0
    end,
    getPercent = function(player)
      return tonumber(player:getSkillLevelPercent(2)) or 0
    end,
    getBase = function(player)
      return tonumber(player:getSkillBaseLevel(2)) or 0
    end
  }
}

function init()
  connect(g_game, {
    onGameStart = online,
    onGameEnd = offline
  })

  if g_game.isOnline() then
    online()
  end
end

function terminate()
  disconnect(g_game, {
    onGameStart = online,
    onGameEnd = offline
  })

  if g_game.isOnline() then
    offline()
  end
end

function online()
  if playerPanel then
    playerPanel:destroy()
  end

  local parent = modules.game_interface.getRootPanel()
  if not parent then
    g_logger.error("GamePlayerPanel: Could not find root game panel to attach to.")
    return
  end

  local loadedUI = g_ui.loadUI('game_playerpanel', parent)
  if not loadedUI then
    g_logger.error("GamePlayerPanel: g_ui.loadUI('game_playerpanel') failed to load.")
    return
  end

  playerPanel = loadedUI
  contentPanel = playerPanel:getChildById('contentPanel')
  topButtonsPanel = playerPanel:getChildById('topButtonsPanel')
  bottomButtonsPanel = playerPanel:getChildById('bottomButtonsPanel')

  if not contentPanel or not topButtonsPanel or not bottomButtonsPanel then
    g_logger.error("GamePlayerPanel: Required panels not found")
    return
  end

  createButtons()
  connectPlayerEvents()

  playerPanel:setVisible(true)
end

function offline()
  disconnectPlayerEvents()

  if playerPanel then
    playerPanel:destroy()
    playerPanel = nil
    contentPanel = nil
    topButtonsPanel = nil
    bottomButtonsPanel = nil
    skillCards = {}
    buttons = {}
  end
end

function createButtons()
  -- Clear existing buttons and setup layouts
  if topButtonsPanel then
    topButtonsPanel:destroyChildren()
    local topLayout = UIHorizontalLayout.create(topButtonsPanel)
    topLayout:setSpacing(5)
    topButtonsPanel:setLayout(topLayout)
  end
  if bottomButtonsPanel then
    bottomButtonsPanel:destroyChildren()
    local bottomLayout = UIHorizontalLayout.create(bottomButtonsPanel)
    bottomLayout:setSpacing(5)
    bottomButtonsPanel:setLayout(bottomLayout)
  end
  buttons = {}

  -- Separate and sort buttons by position
  local topButtons = {}
  local bottomButtons = {}

  for buttonId, config in pairs(buttonConfigs) do
    if config.position == "top" then
      table.insert(topButtons, {id = buttonId, config = config})
    elseif config.position == "bottom" then
      table.insert(bottomButtons, {id = buttonId, config = config})
    end
  end

  table.sort(topButtons, function(a, b) return a.config.order < b.config.order end)
  table.sort(bottomButtons, function(a, b) return a.config.order < b.config.order end)

  createButtonsInPanel(topButtons, topButtonsPanel)
  createButtonsInPanel(bottomButtons, bottomButtonsPanel)
end

function createButtonsInPanel(buttonList, panel)
  local enabledButtons = {}
  for i, buttonData in ipairs(buttonList) do
    if buttonData.config.enabled then
      table.insert(enabledButtons, buttonData)
    end
  end

  if #enabledButtons == 0 then
    return
  end

  for i, buttonData in ipairs(enabledButtons) do
    local buttonId = buttonData.id
    local config = buttonData.config

    local button = g_ui.createWidget('PlayerPanelButton', panel)
    if not button then
      g_logger.error("GamePlayerPanel: Failed to create button: " .. buttonId)
      goto continue
    end

    button:setId(buttonId)
    button:setText(config.text)

    button.onMouseRelease = function(widget, mousePos, mouseButton)
      if mouseButton == MouseLeftButton and widget:containsPoint(mousePos) then
        if config.onClick then
          config.onClick()
        end
        return true
      end
      return false
    end

    buttons[buttonId] = button

    ::continue::
  end

  -- Calculate button widths after UI is rendered
  scheduleEvent(function()
    updatePanelButtonWidths(panel)
  end, 100)
end

function updatePanelButtonWidths(panel)
  local panelButtons = panel:getChildren()
  if #panelButtons == 0 then
    return
  end

  local panelWidth = panel:getWidth()
  local spacing = 5 * (#panelButtons - 1)
  local buttonWidth = math.floor((panelWidth - spacing) / #panelButtons)

  for _, button in ipairs(panelButtons) do
    button:setWidth(buttonWidth)
    button:setFixedSize(true)
  end
end

function connectPlayerEvents()
  local localPlayer = g_game.getLocalPlayer()
  if localPlayer then
    connect(localPlayer, {
      onNinjutsuChange = function(...) onPlayerSkillChange('ninjutsu', ...) end,
      onSkillChange = function(skill, level, levelPercent, oldLevel, oldLevelPercent)
        if skill == 0 then -- Taijutsu
          onPlayerSkillChange('taijutsu', level, levelPercent, oldLevel, oldLevelPercent)
        elseif skill == 1 then -- Kenjutsu
          onPlayerSkillChange('kenjutsu', level, levelPercent, oldLevel, oldLevelPercent)
        elseif skill == 2 then -- Shurikenjutsu
          onPlayerSkillChange('shurikenjutsu', level, levelPercent, oldLevel, oldLevelPercent)
        end
      end
    })
  end

  -- Connect to LocalPlayer class for inventory changes
  connect(LocalPlayer, {
    onInventoryChange = onInventoryChange
  })
end

function disconnectPlayerEvents()
  local localPlayer = g_game.getLocalPlayer()
  if localPlayer then
    disconnect(localPlayer, {
      onNinjutsuChange = function(...) onPlayerSkillChange('ninjutsu', ...) end,
      onSkillChange = function(skill, level, levelPercent, oldLevel, oldLevelPercent)
        if skill == 0 then
          onPlayerSkillChange('taijutsu', level, levelPercent, oldLevel, oldLevelPercent)
        elseif skill == 1 then
          onPlayerSkillChange('kenjutsu', level, levelPercent, oldLevel, oldLevelPercent)
        elseif skill == 2 then
          onPlayerSkillChange('shurikenjutsu', level, levelPercent, oldLevel, oldLevelPercent)
        end
      end
    })
  end

  -- Disconnect from LocalPlayer class
  disconnect(LocalPlayer, {
    onInventoryChange = onInventoryChange
  })
end

function onPlayerSkillChange(skillType, newValue, newPercent, oldValue, oldPercent)
  local config = skillConfigs[skillType]
  if not config then
    g_logger.error("GamePlayerPanel: Unknown skill type: " .. tostring(skillType))
    return
  end

  if skillCards[skillType] and currentContent == "stats" then
    updateSkillCardData(skillType, skillCards[skillType])
  end
end

function showContent(contentType)
  currentContent = contentType

  if contentType == "stats" then
    showStatsContent()
  elseif contentType == "gear" then
    showGearContent()
  elseif contentType == "options" then
    showOptionsContent()
  else
    clearContent()
    showPlaceholderContent(contentType)
  end
end

function clearContent()
  if contentPanel then
    contentPanel:destroyChildren()
    local anchorLayout = UIAnchorLayout.create(contentPanel)
    contentPanel:setLayout(anchorLayout)
    skillCards = {}
    equipmentSlots = {}
  end
end

function showStatsContent()
  clearContent()

  if contentPanel then
    local gridLayout = UIGridLayout.create(contentPanel)
    gridLayout:setCellSize({width = 64, height = 44})
    gridLayout:setCellSpacing(3)
    gridLayout:setFlow(true)
    contentPanel:setLayout(gridLayout)
  end

  for skillType, config in pairs(skillConfigs) do
    skillCards[skillType] = createSkillCard(skillType)
    if not skillCards[skillType] then
      g_logger.error("GamePlayerPanel: Failed to create " .. config.name .. " card")
    end
  end
end

function showGearContent()
  clearContent()

  if contentPanel then
    -- Create equipment layout
    local anchorLayout = UIAnchorLayout.create(contentPanel)
    contentPanel:setLayout(anchorLayout)

    -- Create equipment slots
    createEquipmentSlots()

    -- Update equipment display immediately
    scheduleEvent(function()
      updateEquipmentDisplay()
    end, 50)

    -- Set up periodic refresh while gear panel is open
    scheduleEvent(function()
      if currentContent == "gear" then
        updateEquipmentDisplay()
      end
    end, 500)
  end
end

function showOptionsContent()
  clearContent()

  if contentPanel then
    local label = g_ui.createWidget('UILabel', contentPanel)
    label:setText("Options Panel")
    label:setFont('verdana-11px-antialised')
    label:setColor('#ffffff')
    label:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
    label:addAnchor(AnchorTop, 'parent', AnchorTop)
    label:setMarginTop(20)

    local button = g_ui.createWidget('PlayerPanelWideButton', contentPanel)
    button:setText("Open Options")
    button:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
    button:addAnchor(AnchorTop, 'prev', AnchorBottom)
    button:setMarginTop(10)

    button.onMouseRelease = function(widget, mousePos, mouseButton)
      if mouseButton == MouseLeftButton and widget:containsPoint(mousePos) then
        if modules.client_options then
          modules.client_options.toggle()
        end
        return true
      end
      return false
    end
  end
end

function showPlaceholderContent(contentType)
  if contentPanel then
    local label = g_ui.createWidget('UILabel', contentPanel)
    label:setText(contentType:gsub("^%l", string.upper) .. " content\n(Not implemented yet)")
    label:setFont('verdana-11px-antialised')
    label:setColor('#888888')
    label:setTextAlign(AlignCenter)
    label:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
    label:addAnchor(AnchorVerticalCenter, 'parent', AnchorVerticalCenter)
  end
end

function createSkillCard(skillType)
  if not contentPanel then
    g_logger.error("GamePlayerPanel: contentPanel not found when creating " .. skillType .. " card")
    return nil
  end

  local config = skillConfigs[skillType]
  if not config then
    g_logger.error("GamePlayerPanel: Unknown skill type: " .. skillType)
    return nil
  end

  local card = g_ui.createWidget('SkillStatCard', contentPanel)
  if not card then
    g_logger.error("GamePlayerPanel: Failed to create SkillStatCard widget for " .. skillType)
    return nil
  end

  local iconPlaceholder = card:getChildById('iconPlaceholder')
  if iconPlaceholder then
    iconPlaceholder:setBackgroundColor(config.color)
  end

  updateSkillCardData(skillType, card)
  return card
end

function updateSkillCardData(skillType, card)
  if not card then return end

  local config = skillConfigs[skillType]
  if not config then
    g_logger.error("GamePlayerPanel: Unknown skill type: " .. skillType)
    return
  end

  local localPlayer = g_game.getLocalPlayer()
  if not localPlayer then
    g_logger.error("GamePlayerPanel: No local player found when updating " .. skillType .. " card")
    return
  end

  local skillValue = config.getValue(localPlayer)
  local skillPercent = config.getPercent(localPlayer)
  local baseSkill = config.getBase(localPlayer)

  local statText = card:getChildById('statText')
  if statText then
    statText:setText(math.floor(skillValue) .. "/" .. math.floor(baseSkill))
  end

  local progressBar = card:getChildById('progressBar')
  if progressBar then
    progressBar:setPercent(skillPercent)
  end
end

-- Public API functions for enabling/disabling buttons
function enableButton(buttonId)
  if buttonConfigs[buttonId] then
    buttonConfigs[buttonId].enabled = true
    if playerPanel then
      createButtons()
    end
  end
end

function disableButton(buttonId)
  if buttonConfigs[buttonId] then
    buttonConfigs[buttonId].enabled = false
    if playerPanel then
      createButtons()
    end
  end
end

function addButton(buttonId, config)
  buttonConfigs[buttonId] = config
  if playerPanel then
    createButtons()
  end
end

function createEquipmentSlots()
  if not contentPanel then
    g_logger.error("GamePlayerPanel: contentPanel not found when creating equipment slots")
    return
  end

  -- Create equipment slots in a traditional RPG layout
  for slotName, config in pairs(equipmentSlotConfigs) do
    local slot = g_ui.createWidget('EquipmentSlot', contentPanel)
    if not slot then
      g_logger.error("GamePlayerPanel: Failed to create equipment slot: " .. slotName)
      goto continue
    end

    slot:setId(slotName .. 'Slot')
    slot:setTooltip(config.name)
    slot.slotId = config.slotId
    slot.slotName = slotName
    slot.position = config.position

    -- Set up drag and drop handling
    setupEquipmentSlotDragDrop(slot)

    -- Position the slots in a traditional equipment layout
    positionEquipmentSlot(slot, slotName)

    equipmentSlots[slotName] = slot

    ::continue::
  end
end

function positionEquipmentSlot(slot, slotName)
  -- Traditional RPG equipment layout using absolute positioning for now
  if slotName == "head" then
    slot:addAnchor(AnchorTop, 'parent', AnchorTop)
    slot:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
    slot:setMarginTop(10)
  elseif slotName == "neck" then
    slot:addAnchor(AnchorTop, 'parent', AnchorTop)
    slot:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
    slot:setMarginTop(10)
    slot:setMarginLeft(-50)
  elseif slotName == "armor" then
    slot:addAnchor(AnchorTop, 'parent', AnchorTop)
    slot:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
    slot:setMarginTop(60)
  elseif slotName == "left" then
    slot:addAnchor(AnchorTop, 'parent', AnchorTop)
    slot:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
    slot:setMarginTop(60)
    slot:setMarginLeft(-50)
  elseif slotName == "right" then
    slot:addAnchor(AnchorTop, 'parent', AnchorTop)
    slot:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
    slot:setMarginTop(60)
    slot:setMarginLeft(50)
  elseif slotName == "legs" then
    slot:addAnchor(AnchorTop, 'parent', AnchorTop)
    slot:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
    slot:setMarginTop(110)
  elseif slotName == "feet" then
    slot:addAnchor(AnchorTop, 'parent', AnchorTop)
    slot:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
    slot:setMarginTop(160)
  elseif slotName == "ring" then
    slot:addAnchor(AnchorTop, 'parent', AnchorTop)
    slot:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
    slot:setMarginTop(110)
    slot:setMarginLeft(-50)
  elseif slotName == "ammo" then
    slot:addAnchor(AnchorTop, 'parent', AnchorTop)
    slot:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
    slot:setMarginTop(110)
    slot:setMarginLeft(50)
  elseif slotName == "backpack" then
    slot:addAnchor(AnchorTop, 'parent', AnchorTop)
    slot:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
    slot:setMarginTop(10)
    slot:setMarginLeft(50)
  end
end

function setupEquipmentSlotDragDrop(slot)
  -- Override the onDrop function to handle equipment
  slot.onDrop = function(widget, droppedWidget, mousePos)
    if not droppedWidget or not droppedWidget.currentDragThing then
      return false
    end

    local item = droppedWidget.currentDragThing
    if not item or not item:isItem() then
      return false
    end

    -- Move item to equipment slot
    local toPos = widget.position
    if not toPos then
      g_logger.error("GamePlayerPanel: Equipment slot has no position")
      return false
    end

    g_logger.info("GamePlayerPanel: Equipping item " .. item:getId() .. " to slot " .. widget.slotName)

    -- Send move command to server
    if item:getCount() > 1 then
      modules.game_interface.moveStackableItem(item, toPos)
    else
      g_game.move(item, toPos, 1)
    end

    -- Schedule an update after a short delay to ensure server has processed the move
    scheduleEvent(function()
      updateEquipmentDisplay()
    end, 100)

    return true
  end

  -- Override canAcceptDrop to validate equipment compatibility
  slot.canAcceptDrop = function(widget, droppedWidget, mousePos)
    if not droppedWidget or not droppedWidget.currentDragThing then
      return false
    end

    local item = droppedWidget.currentDragThing
    if not item or not item:isItem() then
      return false
    end

    -- For now, accept all items - server will validate
    -- TODO: Add client-side validation based on item slot properties
    return true
  end
end

function updateEquipmentDisplay()
  local localPlayer = g_game.getLocalPlayer()
  if not localPlayer then
    g_logger.warning("GamePlayerPanel: No local player found for equipment update")
    return
  end

  if currentContent ~= "gear" then
    return
  end

  g_logger.info("GamePlayerPanel: Updating equipment display")

  -- Update each equipment slot with current items
  for slotName, slot in pairs(equipmentSlots) do
    local config = equipmentSlotConfigs[slotName]
    if config and slot then
      local item = localPlayer:getInventoryItem(config.slotId)
      g_logger.info("GamePlayerPanel: Slot " .. slotName .. " (ID: " .. config.slotId .. ") has item: " .. (item and item:getId() or "nil"))

      -- Force clear the slot first to ensure proper visual update
      slot:clearItem()

      -- Then set the actual item if it exists
      scheduleEvent(function()
        if item then
          slot:setItem(item)
        end
      end, 5)
    end
  end
end

-- Event handler for inventory changes
function onInventoryChange(slot, item, oldItem)
  g_logger.info("GamePlayerPanel: onInventoryChange - slot: " .. slot .. ", item: " .. (item and item:getId() or "nil") .. ", oldItem: " .. (oldItem and oldItem:getId() or "nil") .. ", currentContent: " .. currentContent)

  if currentContent == "gear" then
    -- Find the equipment slot that corresponds to this inventory slot
    for slotName, config in pairs(equipmentSlotConfigs) do
      if config.slotId == slot then
        local equipSlot = equipmentSlots[slotName]
        if equipSlot then
          g_logger.info("GamePlayerPanel: Updating equipment slot " .. slotName .. " with item " .. (item and item:getId() or "nil"))

          -- Clear the slot first using the proper clearItem method
          equipSlot:clearItem()

          -- Use a small delay to ensure the clear takes effect, then set the new item
          scheduleEvent(function()
            if item then
              equipSlot:setItem(item)
            end
          end, 10)
        else
          g_logger.warning("GamePlayerPanel: Equipment slot " .. slotName .. " not found")
        end
        break
      end
    end
  end
end