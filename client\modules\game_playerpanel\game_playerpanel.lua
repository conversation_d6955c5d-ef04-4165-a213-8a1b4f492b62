-- game_playerpanel.lua
-- Manages the player panel UI with dynamic button loading

local playerPanel = nil
local contentPanel = nil
local topButtonsPanel = nil
local bottomButtonsPanel = nil
local currentContent = "none"
local skillCards = {}
local buttons = {}

-- Button configuration - defines which buttons are available and where they go
local buttonConfigs = {
  -- Top buttons
  stats = {
    text = "Stats",
    position = "top",
    order = 1,
    enabled = true,
    onClick = function() showContent('stats') end
  },
  gear = {
    text = "Gear",
    position = "top",
    order = 2,
    enabled = true, -- Not implemented yet
    onClick = function() showContent('gear') end
  },
  inventory = {
    text = "Inventory",
    position = "top", 
    order = 3,
    enabled = true, -- Not implemented yet
    onClick = function() showContent('inventory') end
  },
  pvp = {
    text = "PvP",
    position = "top",
    order = 4, 
    enabled = true, -- Not implemented yet
    onClick = function() showContent('pvp') end
  },
  battle = {
    text = "Battle",
    position = "top",
    order = 5,
    enabled = true, -- Not implemented yet
    onClick = function() showContent('battle') end
  },
  
  -- Bottom buttons
  quests = {
    text = "Quests",
    position = "bottom",
    order = 1,
    enabled = true, -- Not implemented yet
    onClick = function() showContent('inventory') end
  },
  spells = {
    text = "Spells",
    position = "bottom",
    order = 2,
    enabled = true, -- Not implemented yet
    onClick = function() showContent('spells') end
  },
  options = {
    text = "Options",
    position = "bottom",
    order = 3,
    enabled = true,
    onClick = function() showContent('options') end
  },
  help = {
    text = "Help",
    position = "bottom",
    order = 4,
    enabled = true, -- Not implemented yet
    onClick = function() showContent('help') end
  },
  logout = {
    text = "Logout",
    position = "bottom",
    order = 5,
    enabled = true, -- Not implemented yet
    onClick = function() showContent('logout') end
  }
}

-- Skill configuration table
local skillConfigs = {
  ninjutsu = {
    name = "Ninjutsu",
    color = "#4a90e2", -- Blue
    getValue = function(player) return tonumber(player:getNinjutsu()) or 0 end,
    getPercent = function(player) return tonumber(player:getNinjutsuPercent()) or 0 end,
    getBase = function(player) return tonumber(player:getBaseNinjutsu()) or 0 end
  },
  taijutsu = {
    name = "Taijutsu", 
    color = "#e74c3c", -- Red
    getValue = function(player) 
      -- Use generic skill system (Taijutsu = 0 in client enum)
      return tonumber(player:getSkillLevel(0)) or 0 
    end,
    getPercent = function(player) 
      return tonumber(player:getSkillLevelPercent(0)) or 0 
    end,
    getBase = function(player) 
      return tonumber(player:getSkillBaseLevel(0)) or 0 
    end
  },
  kenjutsu = {
    name = "Kenjutsu",
    color = "#f39c12", -- Orange
    getValue = function(player) 
      -- Use generic skill system (Kenjutsu = 1 in client enum)
      return tonumber(player:getSkillLevel(1)) or 0 
    end,
    getPercent = function(player) 
      return tonumber(player:getSkillLevelPercent(1)) or 0 
    end,
    getBase = function(player) 
      return tonumber(player:getSkillBaseLevel(1)) or 0 
    end
  },
  shurikenjutsu = {
    name = "Shurikenjutsu",
    color = "#9b59b6", -- Purple
    getValue = function(player) 
      -- Use generic skill system (Shurikenjutsu = 2 in client enum)
      return tonumber(player:getSkillLevel(2)) or 0 
    end,
    getPercent = function(player) 
      return tonumber(player:getSkillLevelPercent(2)) or 0 
    end,
    getBase = function(player) 
      return tonumber(player:getSkillBaseLevel(2)) or 0 
    end
  }
}

function init()
  connect(g_game, {
    onGameStart = online,
    onGameEnd = offline
  })

  if g_game.isOnline() then
    online()
  end
end

function terminate()
  disconnect(g_game, {
    onGameStart = online,
    onGameEnd = offline
  })

  if g_game.isOnline() then
    offline()
  end
end

function online()
  if playerPanel then
    playerPanel:destroy()
  end
  
  local parent = modules.game_interface.getRootPanel()
  if not parent then
    g_logger.error("GamePlayerPanel: Could not find root game panel to attach to.")
    return
  end

  local loadedUI = g_ui.loadUI('game_playerpanel', parent)
  if not loadedUI then
    g_logger.error("GamePlayerPanel: g_ui.loadUI('game_playerpanel') failed to load.")
    return
  end

  playerPanel = loadedUI
  contentPanel = playerPanel:getChildById('contentPanel')
  topButtonsPanel = playerPanel:getChildById('topButtonsPanel')
  bottomButtonsPanel = playerPanel:getChildById('bottomButtonsPanel')
  
  if not contentPanel or not topButtonsPanel or not bottomButtonsPanel then
    g_logger.error("GamePlayerPanel: Required panels not found")
    return
  end
  
  createButtons()
  connectPlayerEvents()
  
  playerPanel:setVisible(true)
end

function offline()
  disconnectPlayerEvents()
  
  if playerPanel then
    playerPanel:destroy()
    playerPanel = nil
    contentPanel = nil
    topButtonsPanel = nil
    bottomButtonsPanel = nil
    skillCards = {}
    buttons = {}
  end
end

function createButtons()
  -- Clear existing buttons and setup layouts
  if topButtonsPanel then 
    topButtonsPanel:destroyChildren() 
    local topLayout = UIHorizontalLayout.create(topButtonsPanel)
    topLayout:setSpacing(5)
    topButtonsPanel:setLayout(topLayout)
  end
  if bottomButtonsPanel then 
    bottomButtonsPanel:destroyChildren() 
    local bottomLayout = UIHorizontalLayout.create(bottomButtonsPanel)
    bottomLayout:setSpacing(5)
    bottomButtonsPanel:setLayout(bottomLayout)
  end
  buttons = {}
  
  -- Separate and sort buttons by position
  local topButtons = {}
  local bottomButtons = {}
  
  for buttonId, config in pairs(buttonConfigs) do
    if config.position == "top" then
      table.insert(topButtons, {id = buttonId, config = config})
    elseif config.position == "bottom" then
      table.insert(bottomButtons, {id = buttonId, config = config})
    end
  end
  
  table.sort(topButtons, function(a, b) return a.config.order < b.config.order end)
  table.sort(bottomButtons, function(a, b) return a.config.order < b.config.order end)
  
  createButtonsInPanel(topButtons, topButtonsPanel)
  createButtonsInPanel(bottomButtons, bottomButtonsPanel)
end

function createButtonsInPanel(buttonList, panel)
  local enabledButtons = {}
  for i, buttonData in ipairs(buttonList) do
    if buttonData.config.enabled then
      table.insert(enabledButtons, buttonData)
    end
  end
  
  if #enabledButtons == 0 then
    return
  end
  
  for i, buttonData in ipairs(enabledButtons) do
    local buttonId = buttonData.id
    local config = buttonData.config
    
    local button = g_ui.createWidget('PlayerPanelButton', panel)
    if not button then
      g_logger.error("GamePlayerPanel: Failed to create button: " .. buttonId)
      goto continue
    end
    
    button:setId(buttonId)
    button:setText(config.text)
    
    button.onMouseRelease = function(widget, mousePos, mouseButton)
      if mouseButton == MouseLeftButton and widget:containsPoint(mousePos) then
        if config.onClick then
          config.onClick()
        end
        return true
      end
      return false
    end
    
    buttons[buttonId] = button
    
    ::continue::
  end
  
  -- Calculate button widths after UI is rendered
  scheduleEvent(function()
    updatePanelButtonWidths(panel)
  end, 100)
end

function updatePanelButtonWidths(panel)
  local panelButtons = panel:getChildren()
  if #panelButtons == 0 then
    return
  end
  
  local panelWidth = panel:getWidth()
  local spacing = 5 * (#panelButtons - 1)
  local buttonWidth = math.floor((panelWidth - spacing) / #panelButtons)
  
  for _, button in ipairs(panelButtons) do
    button:setWidth(buttonWidth)
    button:setFixedSize(true)
  end
end

function connectPlayerEvents()
  local localPlayer = g_game.getLocalPlayer()
  if localPlayer then
    connect(localPlayer, {
      onNinjutsuChange = function(...) onPlayerSkillChange('ninjutsu', ...) end,
      onSkillChange = function(skill, level, levelPercent, oldLevel, oldLevelPercent)
        if skill == 0 then -- Taijutsu
          onPlayerSkillChange('taijutsu', level, levelPercent, oldLevel, oldLevelPercent)
        elseif skill == 1 then -- Kenjutsu
          onPlayerSkillChange('kenjutsu', level, levelPercent, oldLevel, oldLevelPercent)
        elseif skill == 2 then -- Shurikenjutsu
          onPlayerSkillChange('shurikenjutsu', level, levelPercent, oldLevel, oldLevelPercent)
        end
      end
    })
  end
end

function disconnectPlayerEvents()
  local localPlayer = g_game.getLocalPlayer()
  if localPlayer then
    disconnect(localPlayer, {
      onNinjutsuChange = function(...) onPlayerSkillChange('ninjutsu', ...) end,
      onSkillChange = function(skill, level, levelPercent, oldLevel, oldLevelPercent)
        if skill == 0 then
          onPlayerSkillChange('taijutsu', level, levelPercent, oldLevel, oldLevelPercent)
        elseif skill == 1 then
          onPlayerSkillChange('kenjutsu', level, levelPercent, oldLevel, oldLevelPercent)
        elseif skill == 2 then
          onPlayerSkillChange('shurikenjutsu', level, levelPercent, oldLevel, oldLevelPercent)
        end
      end
    })
  end
end

function onPlayerSkillChange(skillType, newValue, newPercent, oldValue, oldPercent)
  local config = skillConfigs[skillType]
  if not config then
    g_logger.error("GamePlayerPanel: Unknown skill type: " .. tostring(skillType))
    return
  end
  
  if skillCards[skillType] and currentContent == "stats" then
    updateSkillCardData(skillType, skillCards[skillType])
  end
end

function showContent(contentType)
  currentContent = contentType
  
  if contentType == "stats" then
    showStatsContent()
  elseif contentType == "options" then
    showOptionsContent()
  else
    clearContent()
    showPlaceholderContent(contentType)
  end
end

function clearContent()
  if contentPanel then
    contentPanel:destroyChildren()
    local anchorLayout = UIAnchorLayout.create(contentPanel)
    contentPanel:setLayout(anchorLayout)
    skillCards = {}
  end
end

function showStatsContent()
  clearContent()
  
  if contentPanel then
    local gridLayout = UIGridLayout.create(contentPanel)
    gridLayout:setCellSize({width = 64, height = 44})
    gridLayout:setCellSpacing(3)
    gridLayout:setFlow(true)
    contentPanel:setLayout(gridLayout)
  end
  
  for skillType, config in pairs(skillConfigs) do
    skillCards[skillType] = createSkillCard(skillType)
    if not skillCards[skillType] then
      g_logger.error("GamePlayerPanel: Failed to create " .. config.name .. " card")
    end
  end
end

function showOptionsContent()
  clearContent()
  
  if contentPanel then
    local label = g_ui.createWidget('UILabel', contentPanel)
    label:setText("Options Panel")
    label:setFont('verdana-11px-antialised')
    label:setColor('#ffffff')
    label:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
    label:addAnchor(AnchorTop, 'parent', AnchorTop)
    label:setMarginTop(20)
    
    local button = g_ui.createWidget('PlayerPanelWideButton', contentPanel)
    button:setText("Open Options")
    button:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
    button:addAnchor(AnchorTop, 'prev', AnchorBottom)
    button:setMarginTop(10)
    
    button.onMouseRelease = function(widget, mousePos, mouseButton)
      if mouseButton == MouseLeftButton and widget:containsPoint(mousePos) then
        if modules.client_options then
          modules.client_options.toggle()
        end
        return true
      end
      return false
    end
  end
end

function showPlaceholderContent(contentType)
  if contentPanel then
    local label = g_ui.createWidget('UILabel', contentPanel)
    label:setText(contentType:gsub("^%l", string.upper) .. " content\n(Not implemented yet)")
    label:setFont('verdana-11px-antialised')
    label:setColor('#888888')
    label:setTextAlign(AlignCenter)
    label:addAnchor(AnchorHorizontalCenter, 'parent', AnchorHorizontalCenter)
    label:addAnchor(AnchorVerticalCenter, 'parent', AnchorVerticalCenter)
  end
end

function createSkillCard(skillType)
  if not contentPanel then
    g_logger.error("GamePlayerPanel: contentPanel not found when creating " .. skillType .. " card")
    return nil
  end
  
  local config = skillConfigs[skillType]
  if not config then
    g_logger.error("GamePlayerPanel: Unknown skill type: " .. skillType)
    return nil
  end
  
  local card = g_ui.createWidget('SkillStatCard', contentPanel)
  if not card then
    g_logger.error("GamePlayerPanel: Failed to create SkillStatCard widget for " .. skillType)
    return nil
  end
  
  local iconPlaceholder = card:getChildById('iconPlaceholder')
  if iconPlaceholder then
    iconPlaceholder:setBackgroundColor(config.color)
  end
  
  updateSkillCardData(skillType, card)
  return card
end

function updateSkillCardData(skillType, card)
  if not card then return end
  
  local config = skillConfigs[skillType]
  if not config then
    g_logger.error("GamePlayerPanel: Unknown skill type: " .. skillType)
    return
  end
  
  local localPlayer = g_game.getLocalPlayer()
  if not localPlayer then
    g_logger.error("GamePlayerPanel: No local player found when updating " .. skillType .. " card")
    return
  end
  
  local skillValue = config.getValue(localPlayer)
  local skillPercent = config.getPercent(localPlayer)
  local baseSkill = config.getBase(localPlayer)
  
  local statText = card:getChildById('statText')
  if statText then
    statText:setText(math.floor(skillValue) .. "/" .. math.floor(baseSkill))
  end
  
  local progressBar = card:getChildById('progressBar')
  if progressBar then
    progressBar:setPercent(skillPercent)
  end
end

-- Public API functions for enabling/disabling buttons
function enableButton(buttonId)
  if buttonConfigs[buttonId] then
    buttonConfigs[buttonId].enabled = true
    if playerPanel then
      createButtons()
    end
  end
end

function disableButton(buttonId)
  if buttonConfigs[buttonId] then
    buttonConfigs[buttonId].enabled = false
    if playerPanel then
      createButtons()
    end
  end
end

function addButton(buttonId, config)
  buttonConfigs[buttonId] = config
  if playerPanel then
    createButtons()
  end
end 