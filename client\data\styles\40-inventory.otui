InventoryItem < Item
  $on:
    image-source: /images/ui/item-blessed

HeadSlot < InventoryItem
  id: slot1
  image-source: /images/game/slots/head
  &position: {x=65535, y=1, z=0}
  $on:
    image-source: /images/game/slots/head-blessed

BodySlot < InventoryItem
  id: slot4
  image-source: /images/game/slots/body
  &position: {x=65535, y=4, z=0}
  $on:
    image-source: /images/game/slots/body-blessed

LegSlot < InventoryItem
  id: slot7
  image-source: /images/game/slots/legs
  &position: {x=65535, y=7, z=0}
  $on:
    image-source: /images/game/slots/legs-blessed

FeetSlot < InventoryItem
  id: slot8
  image-source: /images/game/slots/feet
  &position: {x=65535, y=8, z=0}
  $on:
    image-source: /images/game/slots/feet-blessed

NeckSlot < InventoryItem
  id: slot2
  image-source: /images/game/slots/neck
  &position: {x=65535, y=2, z=0}
  $on:
    image-source: /images/game/slots/neck-blessed

LeftSlot < InventoryItem
  id: slot6
  image-source: /images/game/slots/left-hand
  &position: {x=65535, y=6, z=0}
  $on:
    image-source: /images/game/slots/left-hand-blessed

FingerSlot < InventoryItem
  id: slot9
  image-source: /images/game/slots/finger
  &position: {x=65535, y=9, z=0}
  $on:
    image-source: /images/game/slots/finger-blessed

BackSlot < InventoryItem
  id: slot3
  image-source: /images/game/slots/back
  &position: {x=65535, y=3, z=0}
  $on:
    image-source: /images/game/slots/back-blessed

RightSlot < InventoryItem
  id: slot5
  image-source: /images/game/slots/right-hand
  &position: {x=65535, y=5, z=0}
  $on:
    image-source: /images/game/slots/right-hand-blessed

AmmoSlot < InventoryItem
  id: slot10
  image-source: /images/game/slots/ammo
  &position: {x=65535, y=10, z=0}
  $on:
    image-source: /images/game/slots/ammo-blessed

PurseButton < UIButton
  id: purseButton
  size: 34 12
  !tooltip: tr('Open purse')
  icon-source: /images/game/slots/purse
  icon-clip: 0 0 34 12
  
  $on:
    icon-clip: 0 12 34 12

  $pressed:
    icon-clip: 0 12 34 12

CombatBox < UICheckBox
  size: 20 20
  image-clip: 0 0 20 20
  margin-left: 4

  $checked:
    image-clip: 0 20 20 20


InventoryButton < Button
  font: verdana-11px-antialised
  height: 18
  margin-top: 2
  text-align: center
      
FightOffensiveBox < CombatBox
  image-source: /images/game/combatmodes/fightoffensive
FightBalancedBox < CombatBox
  image-source: /images/game/combatmodes/fightbalanced
FightDefensiveBox < CombatBox
  image-source: /images/game/combatmodes/fightdefensive
ChaseModeBox < CombatBox
  image-source: /images/game/combatmodes/chasemode
SafeFightBox < CombatBox
  image-source: /images/game/combatmodes/safefight

MountButton < CombatBox
  image-source: /images/game/combatmodes/mount    

InventoryWindow < MiniWindow
  icon: /images/topbuttons/inventory
  height: 200
  id: inventoryWindow
  @onClose: modules.game_inventory.onMiniWindowClose()
  &save: true
  &autoOpen: 3

  MiniWindowContents
    anchors.left: parent.left
        
    Panel
      id: inventoryPanel
      margin-right: 63
      margin-top: 2
      anchors.fill: parent
      
      HeadSlot
        anchors.top: parent.top
        anchors.horizontalCenter: parent.horizontalCenter
        margin-top: 3

      BodySlot
        anchors.top: prev.bottom
        anchors.horizontalCenter: prev.horizontalCenter
        margin-top: 3

      LegSlot
        anchors.top: prev.bottom
        anchors.horizontalCenter: prev.horizontalCenter
        margin-top: 3

      FeetSlot
        anchors.top: prev.bottom
        anchors.horizontalCenter: prev.horizontalCenter
        margin-top: 3

      NeckSlot
        anchors.top: slot1.top
        anchors.right: slot1.left
        margin-top: 13
        margin-right: 5

      LeftSlot
        anchors.top: prev.bottom
        anchors.horizontalCenter: prev.horizontalCenter
        margin-top: 3

      FingerSlot
        anchors.top: prev.bottom
        anchors.horizontalCenter: prev.horizontalCenter
        margin-top: 3
        
      BackSlot
        anchors.top: slot1.top
        anchors.left: slot1.right
        margin-top: 13
        margin-left: 5

      RightSlot
        anchors.top: prev.bottom
        anchors.horizontalCenter: prev.horizontalCenter
        margin-top: 3

      AmmoSlot
        anchors.top: prev.bottom
        anchors.horizontalCenter: prev.horizontalCenter
        margin-top: 3

      PurseButton
        anchors.left: slot3.left
        anchors.bottom: slot3.top        
        margin-bottom: 3
        
    Panel
      margin-top: 5
      anchors.fill: parent
      anchors.left: prev.right

      ChaseModeBox
        id: chaseModeBox
        anchors.left: prev.left
        anchors.top: parent.top

      SafeFightBox
        id: safeFightBox
        anchors.left: prev.right
        anchors.top: prev.top
        margin-left: 3
        
      MountButton
        id: mountButton
        anchors.left: chaseModeBox.left
        anchors.top: chaseModeBox.bottom
        margin-left: 0
        margin-top: 3
        
      Panel
        id: buttonsPanel
        margin-top: 4
        margin-right: 5
        anchors.fill: parent
        anchors.top: prev.bottom
        layout:
          type: verticalBox
        
        UIButton
          id: buttonPvp
          height: 20
          icon: /images/game/combatmodes/pvp
          icon-clip: 0 0 42 20

          $on:
            icon-clip: 0 20 42 20
        
        InventoryButton
          !text: tr('Stop')
          @onClick: g_game.stop(); g_game.cancelAttackAndFollow()

        InventoryButton
          !text: tr('Options')
          @onClick: modules.client_options.toggle()

        InventoryButton
          !text: tr('Hotkeys')
          @onClick: modules.game_hotkeys.toggle()

        InventoryButton
          !text: tr('Logout')
          @onClick: modules.game_interface.tryLogout()
