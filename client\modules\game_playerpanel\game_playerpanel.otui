PlayerPanelButton < UIWidget
  font: verdana-11px-antialised
  color: #ffffff
  text-align: center
  background-color: #404040
  border: 1 #606060
  height: 30
  focusable: false

  $hover:
    background-color: #505050
    border-color: #707070
    color: #ffff00

  $pressed:
    background-color: #303030
    border-color: #505050
    color: #cccccc

  $disabled:
    background-color: #2a2a2a
    border-color: #404040
    color: #666666

PlayerPanelWideButton < PlayerPanelButton
  width: 120

SkillStatCard < UIWidget
  width: 64
  height: 44
  background-color: #333333
  border: 2 #555555
  border-radius: 10

  UIWidget
    id: iconPlaceholder
    anchors.top: parent.top
    anchors.left: parent.left
    margin-top: 10
    margin-left: 10
    width: 12
    height: 12
    background-color: #555555
    border: 1 #777777

  UILabel
    id: statText
    anchors.top: parent.top
    anchors.right: parent.right
    margin-top: 10
    margin-right: 2
    text: 75/99
    font: Montserrat-SemiBold-14px_cp1250
    color: #ffffff
    text-align: right

  UIProgressBar
    id: progressBar
    anchors.bottom: parent.bottom
    anchors.left: parent.left
    anchors.right: parent.right
    margin-bottom: 2
    margin-left: 2
    margin-right: 2
    height: 4
    background-color: #1fe90d
    border: 1 #222222

UIWidget
  id: playerPanelContainer
  background-color: #2c2c2c
  border: 1 black
  anchors.bottom: parent.bottom
  anchors.right: parent.right
  width: 287
  height: 450
  visible: true

  // Top buttons container - buttons will be created dynamically
  UIWidget
    id: topButtonsPanel
    anchors.top: parent.top
    anchors.left: parent.left
    anchors.right: parent.right
    height: 40
    margin-top: 5
    margin-left: 5
    margin-right: 5

  // Content area in the middle
  UIWidget
    id: contentPanel
    anchors.top: topButtonsPanel.bottom
    anchors.left: parent.left
    anchors.right: parent.right
    anchors.bottom: bottomButtonsPanel.top
    margin: 5
    background-color: #1a1a1a
    border: 1 #444444
    padding: 6

  // Bottom buttons container - buttons will be created dynamically
  UIWidget
    id: bottomButtonsPanel
    anchors.bottom: parent.bottom
    anchors.left: parent.left
    anchors.right: parent.right
    height: 40
    margin-bottom: 5
    margin-left: 5
    margin-right: 5
